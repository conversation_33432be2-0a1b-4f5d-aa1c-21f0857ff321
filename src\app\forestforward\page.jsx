import React from "react";
import { Navbar2 } from "./_components/Navbar";
import { Footer2 } from "../_components/Footer";
import { Header64 } from "./_components/home/<USER>";
import { Logo3 } from "../_components/shared/Logo3";
import { Stats41 } from "./_components/home/<USER>";
import { Cta13 } from "./_components/home/<USER>";
import { Blog40 } from "./_components/home/<USER>";
import { Cta7 } from "./_components/home/<USER>";
import { Layout394 } from "../_components/shared/EcoLayout";
import { Gallery10 } from "./_components/home/<USER>";
import { Header114 } from "./_components/home/<USER>";
import { ServiceListing } from "../_components/shared/ServiceListing";


const myServices = [
  {
    id: "bedrijfsbos",
    title: "Bedrijfsbos",
    abstractTitle: "BEDRIJFSBOS",
    description: "Een bedrijfsbos is een tastbaar symbool van duurzaam en maatschappelijk engagement. Het versterkt biodiversiteit, slaat CO₂ op, betrekt medewerkers via boomplantacties en verhoogt je aantrekkelijkheid als werkgever.",
    href: "/forestforward/bedrijfsbos",
    backgroundImage: "/images/forestforward/bedrijfsbos/3.png"
  },
  {
    id: "schoolbos",
    title: "Schoolbos",
    abstractTitle: "SCHOOLBOS",
    description: "Learning Leaves verbindt bedrijven en scholen via compacte, biodiverse schoolbossen die educatie, welzijn en ecologische impact combineren. Leerlingen leren over natuur en duurzaamheid, en het bos zorgt voor blijvende lokale impact en wordt wetenschappelijk opgevolgd.",
    href: "/forestforward/schoolbos",
    backgroundImage: "/images/forestforward/schoolbos/7.png"
  },
  {
    id: "voedselbos",
    title: "Voedselbos",
    abstractTitle: "VOEDSELBOS",
    description: "Een voedselbos combineert voedselproductie met biodiversiteit, bodemverbetering en klimaatadaptatie. Het versterkt lokale betrokkenheid, biedt tastbare maatschappelijke meerwaarde en creëert een inspirerende plek die je duurzame ambities zichtbaar maakt voor medewerkers en buurt.",
    href: "/forestforward/voedselbos",
    backgroundImage: "/images/forestforward/voedselbos/4.png"
  },
  {
    id: "natuuropwaardering",
    title: "Natuuropwaardering",
    abstractTitle: "NATUUROPWAARDERING",
    description: "Natuuropwaardering maakt van verwaarloosde groenzones biodiverse, toegankelijke natuur. Dit versterkt biodiversiteit, klimaatweerbaarheid en levenskwaliteit, en biedt bedrijven zichtbare maatschappelijke impact en inspirerende teambuilding.",
    href: "/forestforward/natuuropwaardering",
    backgroundImage: "/images/forestforward/natuuropwaardering/2.png"
  },
  {
    id: "dakboerderij",
    title: "Dakboerderij",
    abstractTitle: "DAKBOERDERIJ",
    description: "Een dakboerderij brengt natuur en lokale voedselproductie terug in de stad. Ze koelt, buffert water, versterkt biodiversiteit en creëert een groene plek voor educatie, beleving en gemeenschapsvorming. Zo bouw je samen aan een leefbare, duurzame stadsomgeving.",
    href: "/forestforward/dakboerderij",
    backgroundImage: "/images/forestforward/dakboerderij/5.png"
  },
  {
    id: "start2forest",
    title: "Start2Forest",
    abstractTitle: "START2FOREST",
    description: "Start2Forest biedt bedrijven de kans om met een beperkt aantal bomen bij te dragen aan een collectief bedrijfsbos. Al vanaf één boom toon je duurzaam engagement; vanaf vijftig mag je mee planten. Bomen zijn ook beschikbaar als groen relatiegeschenk in pakketten vanaf vijf dozen.",
    href: "/forestforward/start2forest",
    backgroundImage: "/images/forestforward/s2f/S2F 1.png"
  },
  {
    id: "boscompensatie",
    title: "Boscompensatie",
    abstractTitle: "BOSCOMPENSATIE",
    description: "Boscompensatie is een wettelijke plicht bij ontbossing, maar ook een kans om ecologie en economie te verzoenen. Forest Forward maakt er een duurzaam project van, met gronden, beheer en opvolging die echte natuurwaarde creëren en maatschappelijke meerwaarde opleveren.",
    href: "/forestforward/boscompensatie",
    backgroundImage: "/images/forestforward/boscompensatie/3.png"
  }
];

export default function Page() {
  return (
    <div>
      <Navbar2 />
      <Header114 />
      <ServiceListing 
        services={myServices}
      />
      {/* <Header64 /> */}
      <Logo3 />
      <Stats41 />
      <Cta13 />
      <Blog40 />
      <Cta7 />
      <Layout394 />
      <Gallery10 />
      <Footer2 />
    </div>
  );
}
