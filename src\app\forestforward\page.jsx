import React from "react";
import { Navbar2 } from "./_components/Navbar";
import { Footer2 } from "../_components/Footer";
import { Header64 } from "./_components/home/<USER>";
import { Logo3 } from "../_components/shared/Logo3";
import { Stats41 } from "./_components/home/<USER>";
import { Cta13 } from "./_components/home/<USER>";
import { Blog40 } from "./_components/home/<USER>";
import { Cta7 } from "./_components/home/<USER>";
import { Layout394 } from "../_components/shared/EcoLayout";
import { Gallery10 } from "./_components/home/<USER>";
import { Header114 } from "./_components/home/<USER>";
import { ServiceListing } from "../_components/shared/ServiceListing";


const myServices = [
  {
    id: "bedrijfsbos",
    title: "Bedrijfsbos",
    abstractTitle: "BEDRIJFSBOS",
    description: "Creëer een eigen bedrijfsbos en versterk uw duurzame bedrijfsvoering. Van planning tot realisatie begeleiden wij uw organisatie naar een groenere toekomst.",
    href: "/forestforward/bedrijfsbos"
  },
  {
    id: "schoolbos",
    title: "Schoolbos",
    abstractTitle: "SCHOOLBOS",
    description: "Educatieve bosprogramma's die kinderen en jongeren verbinden met de natuur. Leren door doen in een groene leeromgeving.",
    href: "/forestforward/schoolbos"
  },
  {
    id: "voedselbos",
    title: "Voedselbos",
    abstractTitle: "VOEDSELBOS",
    description: "Duurzame voedselproductie in harmonie met de natuur. Ontwikkel een voedselbos dat biodiversiteit en voedselzekerheid combineert.",
    href: "/forestforward/voedselbos"
  },
  {
    id: "natuuropwaardering",
    title: "Natuuropwaardering",
    abstractTitle: "NATUUROPWAARDERING",
    description: "Herstel en versterking van bestaande natuurgebieden. Verhoog de biodiversiteit en ecologische waarde van uw grond.",
    href: "/forestforward/natuuropwaardering"
  },
  {
    id: "dakboerderij",
    title: "Dakboerderij",
    abstractTitle: "DAKBOERDERIJ",
    description: "Transformeer daken tot productieve groene ruimtes. Stedelijke landbouw die voedsel produceert en het klimaat verbetert.",
    href: "/forestforward/dakboerderij"
  },
  {
    id: "start2forest",
    title: "Start2Forest",
    abstractTitle: "START2FOREST",
    description: "Begin uw duurzame reis met onze Start2Forest programma's. De eerste stappen naar een groenere organisatie, stap voor stap.",
    href: "/forestforward/start2forest"
  },
  {
    id: "boscompensatie",
    title: "Boscompensatie",
    abstractTitle: "BOSCOMPENSATIE",
    description: "Compenseer uw CO2-uitstoot door het planten van bomen. Een directe en meetbare bijdrage aan klimaatherstel en biodiversiteit.",
    href: "/forestforward/boscompensatie"
  }
];

export default function Page() {
  return (
    <div>
      <Navbar2 />
      <Header114 />
      <ServiceListing 
        services={myServices}
      />
      {/* <Header64 /> */}
      <Logo3 />
      <Stats41 />
      <Cta13 />
      <Blog40 />
      <Cta7 />
      <Layout394 />
      <Gallery10 />
      <Footer2 />
    </div>
  );
}
