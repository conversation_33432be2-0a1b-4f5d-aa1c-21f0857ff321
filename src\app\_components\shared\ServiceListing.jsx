"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";

export function ServiceListing({
  services = [],
  className = "",
}) {
  const [hoveredService, setHoveredService] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);

  const serviceData = services.length > 0 ? services : defaultServices;

  // Track mouse position for floating card
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section
      ref={containerRef}
      className={`relative px-[5%] py-16 md:py-24 lg:py-28 bg-white ${className}`}
    >
      <div className="container">
        

        {/* Service List */}
        <motion.div
          className="space-y-2 md:space-y-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {serviceData.map((service, index) => (
            <motion.div
              key={service.id}
              className="group cursor-pointer py-4 md:py-6 text-center relative"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
            >
              {service.href ? (
                <Link href={service.href} className="block">
                  <h3 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-text-primary group-hover:text-link transition-colors duration-300 tracking-tight">
                    {service.abstractTitle || service.title.toUpperCase()}
                  </h3>
                </Link>
              ) : (
                <h3 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-text-primary group-hover:text-link transition-colors duration-300 tracking-tight">
                  {service.abstractTitle || service.title.toUpperCase()}
                </h3>
              )}

              {/* Short separator line */}
              {index < serviceData.length - 1 && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 md:w-20 h-px bg-border-25"></div>
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Floating Card that follows mouse */}
        <AnimatePresence>
          {hoveredService && (
            <motion.div
              className="fixed z-[9999] pointer-events-none"
              style={{
                left: mousePosition.x + 20,
                top: mousePosition.y - 60,
              }}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{
                opacity: 1,
                scale: 1,
                transition: { duration: 0.2 }
              }}
              exit={{
                opacity: 0,
                scale: 0.9,
                transition: { duration: 0.2 }
              }}
            >
              <div className="bg-background-alternative text-text-alternative p-5 rounded-lg shadow-xlarge border border-border-alternative max-w-xs backdrop-blur-sm">
                {(() => {
                  const service = serviceData.find(s => s.id === hoveredService);
                  return service ? (
                    <div>
                      {/* <h4 className="text-base font-semibold">
                        {service.title}
                      </h4> */}
                      
                      <p className="text-sm leading-relaxed opacity-90">
                        {service.description}
                      </p>
                    </div>
                  ) : null;
                })()}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
}
