"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";

export function ServiceListing({
  services = [],
  className = "",
}) {
  const [hoveredService, setHoveredService] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);

  const serviceData = services.length > 0 ? services : defaultServices;

  // Track mouse position for floating card
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section
      ref={containerRef}
      className={`relative px-[5%] py-16 md:py-24 lg:py-28 bg-white overflow-hidden ${className}`}
    >
      {/* Background Images */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Top left area */}
        <motion.div
          className="absolute top-10 left-10 w-32 h-32 md:w-48 md:h-48 opacity-20 rotate-12"
          initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
          animate={{ opacity: 0.1, scale: 1, rotate: 12 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <img
            src="/images/forestforward/bedrijfsbos/3.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        {/* Top right area */}
        <motion.div
          className="absolute top-20 right-16 w-24 h-24 md:w-36 md:h-36 opacity-18 -rotate-6"
          initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
          animate={{ opacity: 0.08, scale: 1, rotate: -6 }}
          transition={{ duration: 1, delay: 0.4 }}
        >
          <img
            src="/images/forestforward/natuuropwaardering/2.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        {/* Middle left */}
        <motion.div
          className="absolute top-1/3 left-8 w-28 h-28 md:w-40 md:h-40 opacity-22 rotate-45"
          initial={{ opacity: 0, scale: 0.8, rotate: 30 }}
          animate={{ opacity: 0.12, scale: 1, rotate: 45 }}
          transition={{ duration: 1, delay: 0.6 }}
        >
          <img
            src="/images/forestforward/voedselbos/4.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        {/* Middle right */}
        <motion.div
          className="absolute top-1/2 right-12 w-20 h-20 md:w-32 md:h-32 opacity-20 -rotate-12"
          initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
          animate={{ opacity: 0.1, scale: 1, rotate: -12 }}
          transition={{ duration: 1, delay: 0.8 }}
        >
          <img
            src="/images/forestforward/dakboerderij/5.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        {/* Bottom left */}
        <motion.div
          className="absolute bottom-32 left-20 w-36 h-36 md:w-52 md:h-52 opacity-18 rotate-6"
          initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
          animate={{ opacity: 0.08, scale: 1, rotate: 6 }}
          transition={{ duration: 1, delay: 1.0 }}
        >
          <img
            src="/images/forestforward/schoolbos/7.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        {/* Bottom right */}
        <motion.div
          className="absolute bottom-20 right-8 w-24 h-24 md:w-36 md:h-36 opacity-22 -rotate-18"
          initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
          animate={{ opacity: 0.12, scale: 1, rotate: -18 }}
          transition={{ duration: 1, delay: 1.2 }}
        >
          <img
            src="/images/forestforward/boscompensatie/3.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        {/* Additional scattered elements */}
        <motion.div
          className="absolute top-2/3 left-1/4 w-16 h-16 md:w-24 md:h-24 opacity-6 rotate-30"
          initial={{ opacity: 0, scale: 0.8, rotate: 15 }}
          animate={{ opacity: 0.06, scale: 1, rotate: 30 }}
          transition={{ duration: 1, delay: 1.4 }}
        >
          <img
            src="/images/forestforward/s2f/S2F 1.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>

        <motion.div
          className="absolute top-1/4 right-1/3 w-18 h-18 md:w-28 md:h-28 opacity-8 -rotate-24"
          initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
          animate={{ opacity: 0.08, scale: 1, rotate: -24 }}
          transition={{ duration: 1, delay: 1.6 }}
        >
          <img
            src="/images/forestforward/visie/3 Duurzaamheid = een feest.png"
            alt=""
            className="w-full h-full object-cover rounded-lg"
          />
        </motion.div>
      </div>

      <div className="container relative z-10">


        {/* Service List */}
        <motion.div
          className="space-y-2 md:space-y-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {serviceData.map((service, index) => (
            <motion.div
              key={service.id}
              className="group cursor-pointer py-4 md:py-6 text-center relative"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
            >
              {service.href ? (
                <Link href={service.href} className="block">
                  <h3 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-text-primary group-hover:text-link transition-colors duration-300 tracking-tight">
                    {service.abstractTitle || service.title.toUpperCase()}
                  </h3>
                </Link>
              ) : (
                <h3 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-text-primary group-hover:text-link transition-colors duration-300 tracking-tight">
                  {service.abstractTitle || service.title.toUpperCase()}
                </h3>
              )}

              {/* Short separator line */}
              {index < serviceData.length - 1 && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 md:w-20 h-px bg-border-25"></div>
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Floating Card that follows mouse */}
        <AnimatePresence>
          {hoveredService && (
            <motion.div
              className="fixed z-[9999] pointer-events-none"
              style={{
                left: mousePosition.x + 20,
                top: mousePosition.y - 60,
              }}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{
                opacity: 1,
                scale: 1,
                transition: { duration: 0.2 }
              }}
              exit={{
                opacity: 0,
                scale: 0.9,
                transition: { duration: 0.2 }
              }}
            >
              <div className="bg-background-alternative text-text-alternative p-5 rounded-lg shadow-xlarge border border-border-alternative max-w-xs backdrop-blur-sm">
                {(() => {
                  const service = serviceData.find(s => s.id === hoveredService);
                  return service ? (
                    <div>
                      {/* <h4 className="text-base font-semibold">
                        {service.title}
                      </h4> */}
                      
                      <p className="text-sm leading-relaxed opacity-90">
                        {service.description}
                      </p>
                    </div>
                  ) : null;
                })()}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
}
